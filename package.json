{"name": "myapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest"}, "dependencies": {"pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@pinia/testing": "^1.0.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/test-utils": "^2.4.6", "eslint": "^9.27.0", "eslint-plugin-vue": "^10.1.0", "jsdom": "^26.1.0", "vite": "^6.3.5", "vitest": "^3.1.4"}}